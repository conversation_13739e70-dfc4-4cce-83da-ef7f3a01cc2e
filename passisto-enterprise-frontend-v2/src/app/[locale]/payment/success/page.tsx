"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, ArrowRight, Mail, Bot, HeadsetIcon, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useAuth } from "@clerk/nextjs"
import axiosInstance from "@/config/axios"
import { toast } from "sonner"
import { GET_STRIPE_SUBSCRIPTION_DETAILS } from "@/utils/routes"
import { useTranslations } from "next-intl"

interface PlanLimits {
  email_builder: {
    limit: string
    description: string
  }
  ai_interviewer: {
    limit: string
    description: string
  }
  support: {
    limit: string
    description: string
  }
  planName: string
}

export default function PaymentSuccessPage() {
  const t = useTranslations()
  const router = useRouter()
  const { getToken } = useAuth()
  const [planDetails, setPlanDetails] = useState<PlanLimits | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSubscriptionDetails = async () => {
      try {
        const token = await getToken()
        const response = await axiosInstance.get(GET_STRIPE_SUBSCRIPTION_DETAILS, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })
        setPlanDetails(response.data)
        // setPlanDetails({
        //   planName: "Enterprise Plan",
        //   email_builder: {
        //     limit: "1000 emails/month",
        //     description: "AI-powered email templates"
        //   },
        //   ai_interviewer: {
        //     limit: "50 hours/month",
        //     description: "AI interview sessions"
        //   },
        //   support: {
        //     limit: "24/7 Priority",
        //     description: "Premium support"
        //   }
        // })
      } catch (error) {
        console.error('Error fetching subscription details:', error)
        toast.error(t('failed-to-load-subscription-details'))
      } finally {
        setLoading(false)
      }
    }

    fetchSubscriptionDetails()
  }, [getToken])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">{t('loading-subscription-details')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="container max-w-lg px-4">
        <Card className="text-center">
          <CardHeader>
            <div className="flex justify-center mb-4">
              <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold">{t('payment-successful')}</CardTitle>
            <CardDescription>
              {t('welcome-to')} {planDetails?.planName}! {t('your-account-has-been-successfully-upgraded')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-3">
              {planDetails && (
                <>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-primary/5">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <Mail className="h-5 w-5 text-primary" />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">{t('email-builder')}</div>
                      <div className="text-sm text-muted-foreground">
                        {planDetails.email_builder.description} • {planDetails.email_builder.limit}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 rounded-lg bg-primary/5">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <Bot className="h-5 w-5 text-primary" />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">{t('ai-interviewer')}</div>
                      <div className="text-sm text-muted-foreground">
                        {planDetails.ai_interviewer.description} • {planDetails.ai_interviewer.limit}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 rounded-lg bg-primary/5">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <HeadsetIcon className="h-5 w-5 text-primary" />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">t('support')</div>
                      <div className="text-sm text-muted-foreground">
                        {planDetails.support.description} • {planDetails.support.limit}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>

            <Button 
              size="lg" 
              className="w-full"
              onClick={() => router.push("/enterprise/dashboard")}
            >
              {t('go-to-dashboard')}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
