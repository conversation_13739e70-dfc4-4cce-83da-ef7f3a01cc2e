"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Shield,
  Users,
  User,
  Globe,
  Lock,
  Key,
  AlertTriangle,
  Info,
  Plus,
  Minus,
  Check,
  X,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { useTranslations } from "next-intl"

export default function SecurityInfoPage() {
  const t  = useTranslations()
  const router = useRouter()

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('security-system')}</h1>
          <p className="text-muted-foreground mt-2">{t('understanding-the-permission-model-and-access-control')}</p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push("/enterprise/roles-permissions")}
          className="w-full sm:w-auto"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('back-to-roles-and-permissions')}
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="w-full sm:w-auto">
          <TabsTrigger value="overview" className="flex items-center">
            <Info className="mr-2 h-4 w-4" />
            {t('overview-0')}
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center">
            <Key className="mr-2 h-4 w-4" />
            {t('roles')}
          </TabsTrigger>
          <TabsTrigger value="teams" className="flex items-center">
            <Users className="mr-2 h-4 w-4" />
            {t('teams')}
          </TabsTrigger>
          <TabsTrigger value="scopes" className="flex items-center">
            <Globe className="mr-2 h-4 w-4" />
            {t('scopes')}
          </TabsTrigger>
          <TabsTrigger value="best-practices" className="flex items-center">
            <Lock className="mr-2 h-4 w-4" />
            {t('best-practices')}
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>{t('permission-model-overview')}</CardTitle>
              <CardDescription>{t('understanding-how-permissions-work-in-our-enterprise-system')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">{t('multi-layered-permission-system')}</h3>
                <p>
                  {t('our-security-system-uses-a-multi-layered-approach-to-permissions-allowing-for-fine-grained-access-control-while-maintaining-flexibility-and-ease-of-management')}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  <div className="border rounded-lg p-4 bg-muted/20">
                    <div className="flex items-center mb-2">
                      <Key className="h-5 w-5 mr-2 text-blue-500" />
                      <h4 className="font-medium">Role-based</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {t('users-are-assigned-roles-that-grant-a-set-of-permissions-roles-provide-a-baseline-of-access-appropriate-for-different-job-functions')}
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 bg-muted/20">
                    <div className="flex items-center mb-2">
                      <Users className="h-5 w-5 mr-2 text-green-500" />
                      <h4 className="font-medium">Team-based</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {t('users-belong-to-teams-that-grant-additional-permissions-team-permissions-are-typically-scoped-to-the-teams-resources')}
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 bg-muted/20">
                    <div className="flex items-center mb-2">
                      <User className="h-5 w-5 mr-2 text-amber-500" />
                      <h4 className="font-medium">{t('user-overrides')}</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {t('individual-users-can-be-granted-extra-permissions-or-have-inherited-permissions-revoked-to-handle-exceptions')}
                    </p>
                  </div>
                </div>

                <h3 className="text-lg font-semibold mt-8">{t('permission-inheritance-flow')}</h3>
                <div className="border rounded-lg p-6 bg-muted/10">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="flex items-center justify-center w-full">
                      <div className="border rounded-md p-3 bg-blue-50 text-blue-700 font-medium flex items-center">
                        <Key className="h-4 w-4 mr-2" />
                        {t('role-permissions')}
                      </div>
                      <div className="mx-2">+</div>
                      <div className="border rounded-md p-3 bg-green-50 text-green-700 font-medium flex items-center">
                        <Users className="h-4 w-4 mr-2" />
                        {t('team-permissions')}
                      </div>
                    </div>
                    <div className="h-8 border-l-2 border-dashed"></div>
                    <div className="border rounded-md p-3 bg-purple-50 text-purple-700 font-medium flex items-center">
                      <Shield className="h-4 w-4 mr-2" />
                      {t('combined-permissions')}
                    </div>
                    <div className="h-8 border-l-2 border-dashed"></div>
                    <div className="flex items-center justify-center w-full">
                      <div className="border rounded-md p-3 bg-green-50 text-green-700 font-medium flex items-center">
                        <Plus className="h-4 w-4 mr-2" />
                        {t('extra-permissions')}
                      </div>
                      <div className="mx-2">-</div>
                      <div className="border rounded-md p-3 bg-red-50 text-red-700 font-medium flex items-center">
                        <Minus className="h-4 w-4 mr-2" />
                        {t('revoked-permissions')}
                      </div>
                    </div>
                    <div className="h-8 border-l-2 border-dashed"></div>
                    <div className="border rounded-md p-3 bg-primary text-primary-foreground font-medium flex items-center">
                      <Lock className="h-4 w-4 mr-2" />
                      {t('effective-permissions')}
                    </div>
                  </div>
                </div>

                <div className="bg-muted/50 border rounded-lg p-4 flex items-start space-x-3 mt-6">
                  <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium">{t('permission-resolution')}</h3>
                    <p className="text-sm text-muted-foreground">
                      {t('when-determining-if-a-user-has-a-specific-permission-the-system')}
                    </p>
                    <ol className="list-decimal list-inside text-sm text-muted-foreground mt-2 space-y-1">
                      <li>{t('collects-all-permissions-from-the-users-roles')}</li>
                      <li>{t('adds-all-permissions-from-the-users-teams')}</li>
                      <li>{t('adds-any-extra-permissions-granted-directly-to-the-user')}</li>
                      <li>{t('removes-any-permissions-explicitly-revoked-for-the-user')}</li>
                      <li>{t('considers-the-scope-of-each-permission-global-team-project-self')}</li>
                    </ol>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Roles Tab */}
        <TabsContent value="roles">
          <Card>
            <CardHeader>
              <CardTitle>{t('role-based-access-control')}</CardTitle>
              <CardDescription>{t('how-roles-provide-a-foundation-for-permissions-in-the-system')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">{t('what-are-roles')}</h3>
                <p>
                  {t('roles-are-predefined-sets-of-permissions-that-represent-common-job-functions-or-responsibility-levels-within-your-organization-each-user-can-be-assigned-one-or-more-roles')}
                </p>

                <div className="bg-muted/50 border rounded-lg p-4 flex items-start space-x-3 mt-4">
                  <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium">{t('role-structure')}</h3>
                    <p className="text-sm text-muted-foreground">
                      {t('each-role-contains-a-collection-of-permissions-with-specific-scopes-for-example')}
                    </p>
                    <div className="mt-2 space-y-2">
                      <div className="border rounded-md p-3 bg-background">
                        <h4 className="font-medium">{t('administrator-role')}</h4>
                        <div className="mt-2 space-y-1">
                          <div className="flex items-center text-sm">
                            <Shield className="h-3 w-3 mr-2 text-blue-500" />
                            <span>{t('create-user')}</span>
                            <Badge className="ml-2 bg-blue-50 text-blue-700 border-blue-200">{t('global')}</Badge>
                          </div>
                          <div className="flex items-center text-sm">
                            <Shield className="h-3 w-3 mr-2 text-blue-500" />
                            <span>{t('manage-billing')}</span>
                            <Badge className="ml-2 bg-blue-50 text-blue-700 border-blue-200">{t('global-0')}</Badge>
                          </div>
                          <div className="flex items-center text-sm">
                            <Shield className="h-3 w-3 mr-2 text-blue-500" />
                            <span>{t('view-analytics')}</span>
                            <Badge className="ml-2 bg-blue-50 text-blue-700 border-blue-200">{t('global')}</Badge>
                          </div>
                        </div>
                      </div>

                      <div className="border rounded-md p-3 bg-background">
                        <h4 className="font-medium">{t('manager-role')}</h4>
                        <div className="mt-2 space-y-1">
                          <div className="flex items-center text-sm">
                            <Shield className="h-3 w-3 mr-2 text-green-500" />
                            <span>{t('assign-user-to-team')}</span>
                            <Badge className="ml-2 bg-green-50 text-green-700 border-green-200">{t('team')}</Badge>
                          </div>
                          <div className="flex items-center text-sm">
                            <Shield className="h-3 w-3 mr-2 text-green-500" />
                            <span>{t('view-analytics')}</span>
                            <Badge className="ml-2 bg-green-50 text-green-700 border-green-200">{t('team')}</Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <h3 className="text-lg font-semibold mt-6">{t('benefits-of-role-based-access')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">{t('simplified-management')}</h4>
                    <p className="text-sm text-muted-foreground">
                      {t('instead-of-assigning-individual-permissions-to-each-user-you-can-assign-roles-that-contain-predefined-sets-of-permissions-making-user-management-more-efficient')}
                    </p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">{t('standardization')}</h4>
                    <p className="text-sm text-muted-foreground">
                      {t('roles-ensure-that-users-with-similar-responsibilities-have-consistent-access-rights-reducing-the-risk-of-permission-inconsistencies')}
                    </p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">{t('scalability')}</h4>
                    <p className="text-sm text-muted-foreground">
                      {t('as-your-organization-grows-roles-make-it-easier-to-onboard-new-users-with-the-appropriate-access-levels-without-having-to-configure-permissions-individually')}
                    </p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">{t('auditability')}</h4>
                    <p className="text-sm text-muted-foreground">
                      {t('roles-provide-a-clear-structure-for-access-rights-making-it-easier-to-audit-who-has-access-to-what-and-why-they-have-that-access')}
                    </p>
                  </div>
                </div>

                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-start space-x-3 mt-6">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-amber-800">{t('important-note')}</h3>
                    <p className="text-sm text-amber-700">
                      {t('roles-are-defined-at-the-system-level-and-cannot-be-created-or-modified-by-regular-users-contact-your-system-administrator-if-you-need-a-new-role-or-modifications-to-existing-roles')}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Teams Tab */}
        <TabsContent value="teams">
          <Card>
            <CardHeader>
              <CardTitle>{t('team-based-permissions')}</CardTitle>
              <CardDescription>{t('how-teams-provide-contextual-access-to-resources')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">{t('teams-and-permissions')}</h3>
                <p>
                  {t('teams-or-groups-allow-you-to-organize-users-and-grant-permissions-based-on-the-resources-they-need-to-access-teams-are-particularly-useful-for-departmental-or-project-based-access-control')}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <h4 className="font-medium mb-3">{t('team-structure')}</h4>
                    <div className="border rounded-lg p-4 bg-muted/20">
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <Users className="h-5 w-5 mr-2 text-green-500 mt-0.5" />
                          <div>
                            <span className="font-medium">{t('team-members')}</span>
                            <p className="text-sm text-muted-foreground mt-1">
                              {t('users-who-belong-to-the-team-and-inherit-its-permissions')}
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Shield className="h-5 w-5 mr-2 text-green-500 mt-0.5" />
                          <div>
                            <span className="font-medium">{t('team-permissions-0')}</span>
                            <p className="text-sm text-muted-foreground mt-1">
                              {t('permissions-granted-to-all-members-of-the-team-typically-scoped-to-team-resources')}
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Globe className="h-5 w-5 mr-2 text-green-500 mt-0.5" />
                          <div>
                            <span className="font-medium">{t('resource-scope')}</span>
                            <p className="text-sm text-muted-foreground mt-1">
                              {t('the-specific-resources-projects-data-etc-that-the-team-has-access-to')}
                            </p>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">
                      
                    </h4>
                    <div className="border rounded-lg p-4 bg-background">
                      <div className="flex items-center mb-3">
                        <Users className="h-5 w-5 mr-2 text-primary" />
                        <h5 className="font-medium">{t('engineering-team')}</h5>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <h6 className="text-sm font-medium">{t('team-members-0')}</h6>
                          <div className="flex flex-wrap gap-1 mt-1">
                            <Badge variant="outline">{t('john-doe')}</Badge>
                            <Badge variant="outline">{t('jane-smith')}</Badge>
                            <Badge variant="outline">{t('robert-johnson')}</Badge>
                          </div>
                        </div>

                        <div>
                          <h6 className="text-sm font-medium">{t('team-permissions-1')}</h6>
                          <div className="space-y-1 mt-1">
                            <div className="flex items-center text-sm">
                              <Shield className="h-3 w-3 mr-2 text-green-500" />
                              <span>{t('create-data-provider')}</span>
                              <Badge className="ml-2 bg-green-50 text-green-700 border-green-200">{t('team-0')}</Badge>
                            </div>
                            <div className="flex items-center text-sm">
                              <Shield className="h-3 w-3 mr-2 text-green-500" />
                              <span>{t('view-analytics-0')}</span>
                              <Badge className="ml-2 bg-green-50 text-green-700 border-green-200">{t('team-1')}</Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <h3 className="text-lg font-semibold mt-6">{t('team-permission-scopes')}</h3>
                <p>
                  {t('team-permissions-are-typically-scoped-to-the-teams-resources-but-they-can-also-have-different-scope-types')}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Users className="h-4 w-4 mr-2 text-green-500" />
                      <h4 className="font-medium">{t('team-scope')}</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {t('permissions-apply-only-to-resources-owned-by-the-team-this-is-the-most-common-scope-for-team-permissions')}
                    </p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Globe className="h-4 w-4 mr-2 text-blue-500" />
                      <h4 className="font-medium">{t('global-scope')}</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {t('some-team-permissions-may-have-global-scope-allowing-team-members-to-perform-actions-across-the-entire-system')}
                    </p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Shield className="h-4 w-4 mr-2 text-purple-500" />
                      <h4 className="font-medium">{t('project-scope')}</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {t('permissions-may-be-limited-to-specific-projects-that-the-team-is-working-on-even-if-those-projects-are-shared-with-other-teams')}
                    </p>
                  </div>
                </div>

                <div className="bg-muted/50 border rounded-lg p-4 flex items-start space-x-3 mt-6">
                  <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium">{t('team-vs-role-permissions')}</h3>
                    <p className="text-sm text-muted-foreground">
                      {t('while-roles-define-what-a-user-can-do-based-on-their-job-function-teams-define-what-resources-they-can-access-this-combination-provides-a-flexible-and-powerful-access-control-system')}
                    </p>
                    <ul className="list-disc list-inside text-sm text-muted-foreground mt-2 space-y-1">
                      <li>{t('a-user-with-the-manager-role-in-the-engineering-team-can-manage-engineering-resources')}</li>
                      <li>{t('the-same-user-in-the-marketing-team-can-manage-marketing-resources')}</li>
                      <li>{t('different-teams-can-have-different-permission-sets-based-on-their-needs')}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scopes Tab */}
        <TabsContent value="scopes">
          <Card>
            <CardHeader>
              <CardTitle>{t('permission-scopes')}</CardTitle>
              <CardDescription>{t('understanding-how-permission-scopes-limit-the-reach-of-permissions')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">{t('what-are-permission-scopes')}</h3>
                <p>
                  {t('permission-scopes-define-the-boundaries-within-which-a-permission-applies-they-allow-for-fine-grained-control-over-what-resources-a-user-can-access-with-a-given-permission')}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div className="border rounded-lg p-5 bg-muted/10">
                    <h4 className="font-medium mb-4">{t('scope-types')}</h4>
                    <div className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <div className="bg-blue-50 p-2 rounded-full">
                          <Globe className="h-5 w-5 text-blue-500" />
                        </div>
                        <div>
                          <h5 className="font-medium">GLOBAL</h5>
                          <p className="text-sm text-muted-foreground mt-1">
                            {t('permission-applies-across-the-entire-system-to-all-resources-of-the-relevant-type')}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-3">
                        <div className="bg-green-50 p-2 rounded-full">
                          <Users className="h-5 w-5 text-green-500" />
                        </div>
                        <div>
                          <h5 className="font-medium">TEAM</h5>
                          <p className="text-sm text-muted-foreground mt-1">
                            {t('permission-applies-only-to-resources-owned-by-or-associated-with-the-users-team-s')}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-3">
                        <div className="bg-purple-50 p-2 rounded-full">
                          <Shield className="h-5 w-5 text-purple-500" />
                        </div>
                        <div>
                          <h5 className="font-medium">PROJECT</h5>
                          <p className="text-sm text-muted-foreground mt-1">
                            {t('permission-applies-only-to-specific-projects-regardless-of-team-ownership')}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-3">
                        <div className="bg-amber-50 p-2 rounded-full">
                          <User className="h-5 w-5 text-amber-500" />
                        </div>
                        <div>
                          <h5 className="font-medium">SELF</h5>
                          <p className="text-sm text-muted-foreground mt-1">
                            {t('permission-applies-only-to-resources-created-by-or-directly-assigned-to-the-user')}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-4">{t('scope-examples')}</h4>
                    <div className="space-y-4">
                      <div className="border rounded-md p-3 bg-background">
                        <div className="flex items-center">
                          <Shield className="h-4 w-4 mr-2 text-blue-500" />
                          <span className="font-medium">{t('view-analytics-with-global-scope')}</span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-2">
                          {t('user-can-view-analytics-for-all-teams-and-projects-in-the-system')}
                        </p>
                      </div>

                      <div className="border rounded-md p-3 bg-background">
                        <div className="flex items-center">
                          <Shield className="h-4 w-4 mr-2 text-green-500" />
                          <span className="font-medium">{t('create-data-provider-with-team-scope')}</span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-2">
                          {t('user-can-create-data-providers-only-for-their-team-s')}
                        </p>
                      </div>

                      <div className="border rounded-md p-3 bg-background">
                        <div className="flex items-center">
                          <Shield className="h-4 w-4 mr-2 text-purple-500" />
                          <span className="font-medium">{t('export-data-with-project-scope')}</span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-2">
                          {t('user-can-export-data-only-from-specific-projects-they-have-access-to')}
                        </p>
                      </div>

                      <div className="border rounded-md p-3 bg-background">
                        <div className="flex items-center">
                          <Shield className="h-4 w-4 mr-2 text-amber-500" />
                          <span className="font-medium">{t('manage-profile-with-self-scope')}</span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-2">
                          {t('user-can-only-manage-their-own-profile-information')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <h3 className="text-lg font-semibold mt-6">{t('scope-resolution')}</h3>
                <p>
                  {t('when-a-user-has-the-same-permission-with-different-scopes-e-g-from-different-roles-or-teams-the-system-uses-the-broadest-scope')}
                </p>

                <div className="border rounded-lg p-4 bg-muted/20 mt-4">
                  <h4 className="font-medium mb-3">{t('scope-hierarchy-broadest-to-narrowest')}</h4>
                  <div className="flex flex-col md:flex-row items-center justify-between space-y-2 md:space-y-0">
                    <div className="flex items-center">
                      <div className="bg-blue-50 p-2 rounded-full">
                        <Globe className="h-5 w-5 text-blue-500" />
                      </div>
                      <span className="ml-2 font-medium">GLOBAL</span>
                    </div>
                    <div className="text-muted-foreground">→</div>
                    <div className="flex items-center">
                      <div className="bg-green-50 p-2 rounded-full">
                        <Users className="h-5 w-5 text-green-500" />
                      </div>
                      <span className="ml-2 font-medium">TEAM</span>
                    </div>
                    <div className="text-muted-foreground">→</div>
                    <div className="flex items-center">
                      <div className="bg-purple-50 p-2 rounded-full">
                        <Shield className="h-5 w-5 text-purple-500" />
                      </div>
                      <span className="ml-2 font-medium">PROJECT</span>
                    </div>
                    <div className="text-muted-foreground">{t('key-0')}</div>
                    <div className="flex items-center">
                      <div className="bg-amber-50 p-2 rounded-full">
                        <User className="h-5 w-5 text-amber-500" />
                      </div>
                      <span className="ml-2 font-medium">SELF</span>
                    </div>
                  </div>
                </div>

                <div className="bg-muted/50 border rounded-lg p-4 flex items-start space-x-3 mt-6">
                  <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium">{t('example-scope-resolution')}</h3>
                    <p className="text-sm text-muted-foreground">{t('if-a-user-has-the-view-analytics-permission-from')}</p>
                    <ul className="list-disc list-inside text-sm text-muted-foreground mt-2 space-y-1">
                      <li>{t('their-manager-role-with-team-scope')}</li>
                      <li>{t('the-leadership-team-with-global-scope')}</li>
                    </ul>
                    <p className="text-sm text-muted-foreground mt-2">
                      {t('the-effective-scope-will-be-global-as-its-broader-than-team')}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Best Practices Tab */}
        <TabsContent value="best-practices">
          <Card>
            <CardHeader>
              <CardTitle>{t('security-best-practices')}</CardTitle>
              <CardDescription>{t('guidelines-for-implementing-effective-access-control')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">{t('principle-of-least-privilege')}</h3>
                <p>
                  {t('always-grant-users-the-minimum-permissions-necessary-to-perform-their-job-functions-this-reduces-the-risk-of-accidental-or-intentional-misuse-of-privileges')}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  <div>
                    <h4 className="font-medium mb-3">{t('role-assignment')}</h4>
                    <div className="space-y-4">
                      <div className="border rounded-lg p-4 bg-green-50">
                        <h5 className="font-medium text-green-700 flex items-center">
                          <Check className="h-4 w-4 mr-2" />
                          {t('do')}
                        </h5>
                        <ul className="list-disc list-inside text-sm text-green-700 mt-2 space-y-1">
                          <li>{t('assign-roles-based-on-job-responsibilities')}</li>
                          <li>{t('regularly-review-and-audit-role-assignments')}</li>
                          <li>{t('remove-unnecessary-roles-when-job-functions-change')}</li>
                          <li>{t('use-the-most-restrictive-role-that-meets-the-users-needs')}</li>
                        </ul>
                      </div>

                      <div className="border rounded-lg p-4 bg-red-50">
                        <h5 className="font-medium text-red-700 flex items-center">
                          <X className="h-4 w-4 mr-2" />
                          Don't
                        </h5>
                        <ul className="list-disc list-inside text-sm text-red-700 mt-2 space-y-1">
                          <li>{t('assign-administrator-roles-unnecessarily')}</li>
                          <li>{t('create-roles-with-excessive-permissions')}</li>
                          <li>{t('assign-multiple-roles-when-one-would-suffice')}</li>
                          <li>{t('leave-unused-roles-assigned-to-users')}</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">{t('team-management')}</h4>
                    <div className="space-y-4">
                      <div className="border rounded-lg p-4 bg-green-50">
                        <h5 className="font-medium text-green-700 flex items-center">
                          <Check className="h-4 w-4 mr-2" />
                          {t('do')}
                        </h5>
                        <ul className="list-disc list-inside text-sm text-green-700 mt-2 space-y-1">
                          <li>{t('organize-teams-based-on-functional-areas')}</li>
                          <li>{t('limit-team-permissions-to-necessary-resources')}</li>
                          <li>{t('regularly-review-team-memberships')}</li>
                          <li>{t('use-team-scoped-permissions-when-possible')}</li>
                        </ul>
                      </div>

                      <div className="border rounded-lg p-4 bg-red-50">
                        <h5 className="font-medium text-red-700 flex items-center">
                          <X className="h-4 w-4 mr-2" />
                          Don't
                        </h5>
                        <ul className="list-disc list-inside text-sm text-red-700 mt-2 space-y-1">
                          <li>{t('create-teams-with-overlapping-responsibilities')}</li>
                          <li>{t('grant-global-permissions-to-teams-unnecessarily')}</li>
                          <li>{t('t-add-users-to-teams-they-dont-need-to-be-in')}</li>
                          <li>{t('create-teams-solely-for-permission-management')}</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <h3 className="text-lg font-semibold mt-6">{t('permission-overrides')}</h3>
                <p>
                  {t('use-permission-overrides-sparingly-and-only-when-necessary-they-should-be-the-exception-not-the-rule')}
                </p>

                <div className="border rounded-lg p-4 bg-muted/20 mt-4">
                  <h4 className="font-medium mb-3">{t('when-to-use-permission-overrides')}</h4>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <Plus className="h-5 w-5 mr-2 text-green-500 mt-0.5" />
                      <div>
                        <span className="font-medium">{t('extra-permissions-0')}</span>
                        <p className="text-sm text-muted-foreground mt-1">
                          {t('grant-when-a-user-needs-a-specific-permission-that-isnt-included-in-their-roles-or-teams-but-doesnt-warrant-a-role-change')}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <Minus className="h-5 w-5 mr-2 text-red-500 mt-0.5" />
                      <div>
                        <span className="font-medium">{t('revoked-permissions-0')}</span>
                        <p className="text-sm text-muted-foreground mt-1">
                          {t('use-when-a-user-has-a-role-or-team-membership-that-grants-permissions-they-shouldnt-have-but-they-still-need-the-role-or-team-for-other-permissions')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-start space-x-3 mt-6">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-amber-800">{t('caution-with-overrides')}</h3>
                    <p className="text-sm text-amber-700">
                      {t('excessive-use-of-permission-overrides-can-make-your-security-model-difficult-to-understand-and-audit-if-you-find-yourself-frequently-using-overrides-consider')}
                    </p>
                    <ul className="list-disc list-inside text-sm text-amber-700 mt-2 space-y-1">
                      <li>{t('reviewing-and-adjusting-your-role-definitions')}</li>
                      <li>{t('creating-more-specialized-teams-with-appropriate-permissions')}</li>
                      <li>{t('implementing-a-more-granular-permission-model')}</li>
                    </ul>
                  </div>
                </div>

                <h3 className="text-lg font-semibold mt-6">{t('regular-security-audits')}</h3>
                <p>{t('conduct-regular-audits-of-your-permission-system-to-ensure-it-remains-effective-and-secure')}</p>

                <div className="border rounded-lg p-4 bg-muted/20 mt-4">
                  <h4 className="font-medium mb-3">{t('audit-checklist')}</h4>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span className="text-sm">{t('review-user-role-assignments-for-appropriateness')}</span>
                    </div>
                    <div className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span className="text-sm">{t('verify-team-memberships-and-permissions')}</span>
                    </div>
                    <div className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span className="text-sm">{t('identify-and-review-permission-overrides')}</span>
                    </div>
                    <div className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span className="text-sm">{t('check-for-users-with-excessive-permissions')}</span>
                    </div>
                    <div className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span className="text-sm">{t('remove-permissions-for-inactive-users')}</span>
                    </div>
                    <div className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span className="text-sm">{t('document-any-exceptions-to-standard-security-policies')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}