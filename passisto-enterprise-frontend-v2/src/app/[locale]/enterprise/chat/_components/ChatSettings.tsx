"use client"

import React from "react"
import { Settings, Database, Server, Globe } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useChatSettings, ProviderType } from "@/context/ChatSettingsContext"

interface Alias {
  id: string;
  name: string;
}

export default function ChatSettings() {
  const {
    filteredAliases,
    toggleAlias,
    isAliasSelected,
    selectedAliases,
    isLoading,
    error,
    providerFilter,
    setProviderFilter
  } = useChatSettings()
  const [open, setOpen] = React.useState(false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8" aria-label="Settings">
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Chat Settings</DialogTitle>
          <DialogDescription>
            Configure your chat experience
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label className="text-base font-medium">
              Knowledge Bases
            </Label>
            <p className="text-sm text-muted-foreground mb-4">
              Select which knowledge bases to search when asking questions
            </p>

            {error ? (
              <div className="text-sm text-red-500 mb-2">
                Error loading knowledge bases: {error}
              </div>
            ) : null}

            {/* Filter tabs */}
            <Tabs
              value={providerFilter}
              onValueChange={(value) => setProviderFilter(value as ProviderType)}
              className="mb-4"
            >
              <TabsList className="grid grid-cols-4 w-full">
                <TabsTrigger value="all" className="text-xs">
                  <Database className="h-3 w-3 mr-1" />
                  All
                </TabsTrigger>
                <TabsTrigger value="ftp" className="text-xs">
                  <Server className="h-3 w-3 mr-1" />
                  FTP
                </TabsTrigger>
                <TabsTrigger value="jira" className="text-xs">
                  <Settings className="h-3 w-3 mr-1" />
                  Jira
                </TabsTrigger>
                <TabsTrigger value="web" className="text-xs">
                  <Globe className="h-3 w-3 mr-1" />
                  Web
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {isLoading ? (
              <div className="text-sm text-muted-foreground">Loading knowledge bases...</div>
            ) : (
              <div className="space-y-3">
                {filteredAliases.map((alias: Alias) => (
                <div key={alias.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`alias-${alias.id}`}
                    checked={isAliasSelected(alias.id)}
                    onCheckedChange={() => toggleAlias(alias)}
                    disabled={isLoading || (isAliasSelected(alias.id) && selectedAliases.length === 1)}
                  />
                  <Label
                    htmlFor={`alias-${alias.id}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {alias.name}
                  </Label>
                </div>
              ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
