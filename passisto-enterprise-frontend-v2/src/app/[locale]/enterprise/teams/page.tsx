"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { UsersRound, Search } from "lucide-react";
import { toast } from "sonner";
import { ViewToggle } from "@/components/view-toggle";
import { Pagination } from "@/components/pagination";
import { TeamCardView } from "./_components/team-card-view";
import { TeamTableView } from "./_components/team-table-view";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchTeams, Team, deleteTeam } from "@/store/slices/teamSlice";
import { useAuth } from "@clerk/nextjs";
import axios from "axios";
import { teamPermissions, checkPermissions, integrationPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useRouter } from "next/navigation";

export default function TeamsPage() {
  const router = useRouter();
  const { getToken } = useAuth();
  const dispatch = useAppDispatch();
  const { teams, status, error } = useAppSelector((state) => state.teams);
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  // Add permission check at the beginning of the component
  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];
      
      // Check if user has at least one team-related permission using the helper function
      const hasAnyTeamPermission = checkPermissions(permissions, [
        teamPermissions.canView,
        teamPermissions.canCreate,
        teamPermissions.canUpdate,
        teamPermissions.canDelete,
        teamPermissions.canAssignUser,
        teamPermissions.canRemoveUser,
        integrationPermissions.canAssignToGroup,
        integrationPermissions.canRemoveFromGroup,
      ]);
      
      // Redirect if user doesn't have any team-related permissions
      if (!hasAnyTeamPermission) {
        toast.error("Permission denied", {
          description: "You don't have permission to access the teams page."
        });
        router.push("/enterprise/dashboard");
      }
    }
  }, [backendUser, backendUserLoading, router]);

  const [searchQuery, setSearchQuery] = useState("");
  const [teamToDelete, setTeamToDelete] = useState<Team | null>(null);
  const [view, setView] = useState("card"); // Default to card view
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6);

  // Load teams from Redux store and preferences
  useEffect(() => {
    const loadTeamsAndPreferences = async () => {
      try {
        setLoading(true);

        // Get token from Clerk
        const token = await getToken();
        console.log(token);
        const getintegration = axios.get(
          "https://pe.passisto.com/api/v1/integrations",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        // Dispatch the fetchTeams action
        await dispatch(fetchTeams(token!)).unwrap();

        // Load view preference
        const savedView = localStorage.getItem("teamsViewPreference");
        if (savedView) {
          setView(savedView);
        }

        // Load items per page preference
        const savedItemsPerPage = localStorage.getItem("teamsItemsPerPage");
        if (savedItemsPerPage) {
          setItemsPerPage(Number(savedItemsPerPage));
        }
      } catch (error) {
        console.error("Error loading teams:", error);
        toast.error("Error loading teams", {
          description: "There was an error loading your teams data.",
        });
      } finally {
        setLoading(false);
      }
    };

    loadTeamsAndPreferences();
  }, [dispatch, getToken]);

  // Save view preference to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("teamsViewPreference", view);
  }, [view]);

  // Save items per page preference
  useEffect(() => {
    localStorage.setItem("teamsItemsPerPage", itemsPerPage.toString());
  }, [itemsPerPage]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const filteredTeams = teams.filter(
    (team) =>
      team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      team.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Calculate pagination
  const totalPages = Math.ceil(filteredTeams.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredTeams.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleDeleteTeam = async () => {
    if (!teamToDelete) return;

    // Prevent deletion of the "Company" team
    if (teamToDelete.name === "Company") {
      toast.error("Cannot delete default team", {
        description: "The default 'Company' team cannot be deleted.",
      });
      setTeamToDelete(null);
      return;
    }

    try {
      setIsSubmitting(true);

      // Get authentication token
      const token = await getToken();

      if (!token) {
        toast.error("Authentication required");
        return;
      }

      // Dispatch deleteTeam action
      await dispatch(
        deleteTeam({
          teamId: teamToDelete.id,
          token,
        })
      ).unwrap();

      toast.success("Team deleted", {
        description: `${teamToDelete.name} has been deleted successfully.`,
      });

      setTeamToDelete(null);
    } catch (error: any) {
      console.error("Error deleting team:", error);
      toast.error("Error deleting team", {
        description: error.message || "Failed to delete the team.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading || status === "loading") {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">Loading teams..</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <p className="text-red-500 mb-4">Error loading teams: {error}</p>
        {/* <Button onClick={() => dispatch(fetchTeams(getToken()))}>Retry</Button> */}
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Teams</h1>
          <p className="text-muted-foreground mt-2">
            Manage your organization's teams.
          </p>
        </div>
        {teamPermissions.canCreate(backendUser?.permissions ?? []) && (
          <Button asChild>
            <Link href="/enterprise/teams/new">
              <UsersRound className="mr-2 h-4 w-4" />
              Create Team
            </Link>
          </Button>
        )}
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1 w-full">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search teams..."
            className="pl-8 w-full"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <ViewToggle view={view} setView={setView} />
      </div>

      {filteredTeams.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-muted-foreground">
            No teams found. Create your first team to get started.
          </p>
        </div>
      ) : (
        <>
          {view === "card" ? (
            <TeamCardView 
              teams={currentItems} 
              onDeleteTeam={setTeamToDelete} 
              backendUser={backendUser}
            />
          ) : (
            <TeamTableView
              teams={currentItems}
              onDeleteTeam={setTeamToDelete}
              backendUser={backendUser}
            />
          )}
          {filteredTeams.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={handleItemsPerPageChange}
              totalItems={filteredTeams.length}
              showingFrom={indexOfFirstItem + 1}
              showingTo={Math.min(indexOfLastItem, filteredTeams.length)}
              viewMode={view}
            />
          )}
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!teamToDelete}
        onOpenChange={(open) => !open && setTeamToDelete(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Team</DialogTitle>
            <DialogDescription>
              {teamToDelete?.name === "Company" ? (
                <span className="text-destructive">
                  The default 'Company' team cannot be deleted.
                </span>
              ) : (
                <>
                  Are you sure you want to delete {teamToDelete?.name}? This
                  action cannot be undone and will remove all team associations.
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setTeamToDelete(null)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteTeam}
              disabled={isSubmitting || teamToDelete?.name === "Company"}
            >
              {isSubmitting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
