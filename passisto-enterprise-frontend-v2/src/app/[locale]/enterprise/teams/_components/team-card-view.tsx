"use client"

import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { UsersRound, Pencil, Trash2, Eye, Database, MoreHorizontal, Info, UserPlus } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Team } from "@/store/slices/teamSlice"
import { useRouter } from "next/navigation"
import { teamPermissions , integrationPermissions} from "@/utils/ACTION_PERMISSIONS";
import { BackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"

interface TeamCardViewProps {
  teams: Team[]
  onDeleteTeam: (team: Team) => void
  backendUser: BackendUser | null
}

export function TeamCardView({ teams, onDeleteTeam, backendUser }: TeamCardViewProps) {
  const t = useTranslations()
  const router = useRouter()
  
  if (teams.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/10">
        <UsersRound className="h-10 w-10 text-muted-foreground mb-2" />
        <h3 className="text-lg font-medium">{t('t-no-teams-found')}</h3>
        <p className="text-sm text-muted-foreground mt-1 mb-4">
          {t('no-teams-match-your-search-criteria-or-no-teams-have-been-created-yet')}
        </p>
        <Button asChild>
          <Link href="/enterprise/teams/new">{t('create-team')}</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {teams.map((team) => (
        <Card key={team.id} className="overflow-hidden border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2 space-y-1">
            <div className="flex justify-between items-start">
              <CardTitle className="text-lg font-semibold truncate">{team.name}</CardTitle>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">{t('open-menu')}</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>{t('actions-0')}</DropdownMenuLabel>
                  
                  {teamPermissions.canView(backendUser?.permissions ?? []) && (
                    <DropdownMenuItem onClick={() => router.push(`/enterprise/teams/${team.id}/view`)}>
                      <Eye className="mr-2 h-4 w-4" />
                      {t('view-details')}
                    </DropdownMenuItem>
                  )}
                  
                  {teamPermissions.canUpdate(backendUser?.permissions ?? []) && (
                    <DropdownMenuItem onClick={() => router.push(`/enterprise/teams/${team.id}`)}>
                      <Pencil className="mr-2 h-4 w-4" />
                      {t('edit-0')}
                    </DropdownMenuItem>
                  )}
                  
                  {teamPermissions.canAssignUser(backendUser?.permissions ?? []) && (
                    <DropdownMenuItem onClick={() => router.push(`/enterprise/teams/${team.id}/members`)}>
                      <UsersRound className="mr-2 h-4 w-4" />
                      {t('members-0')}
                    </DropdownMenuItem>
                  )}
                  
                  {integrationPermissions.canAssignToGroup(backendUser?.permissions ?? []) && (
                    <DropdownMenuItem onClick={() => router.push(`/enterprise/teams/${team.id}/integrations`)}>
                      <Database className="mr-2 h-4 w-4" />
                      {t('assign-integration')}
                    </DropdownMenuItem>
                  )}
                  
                  {teamPermissions.canDelete(backendUser?.permissions ?? []) && (
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive cursor-pointer"
                      onClick={() => onDeleteTeam(team)}
                      disabled={team.name === 'Company'}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {team.name === 'Company' ? t('cannot-delete-default-team') : t('delete')}
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <CardDescription className="line-clamp-2 h-10">
              {team.description || t('no-description-provided-0')}
            </CardDescription>
          </CardHeader>
          <CardContent className="pb-3">
            <div className="flex items-center text-sm text-muted-foreground">
              <UsersRound className="mr-1 h-4 w-4" />
              <span>{team.memberCount} members</span>
            </div>
            <div className="mt-3 flex flex-wrap gap-2">
              {team.permissions && team.permissions.length > 0 ? (
                <>
                  {team.permissions.slice(0, 3).map((perm, i) => {
                    const permName = typeof perm === "string" ? perm : perm.action;
                    return (
                      <Badge key={i} variant="secondary" className="text-xs">
                        {permName?.replace("CAN_", "").replace(/_/g, " ").toLowerCase()}
                      </Badge>
                    );
                  })}
                  {team.permissions.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{team.permissions.length - 3} more
                    </Badge>
                  )}
                </>
              ) : (
                <Badge variant="outline" className="text-xs text-muted-foreground">
                  {t('no-permissions')}
                </Badge>
              )}
            </div>
          </CardContent>
          <CardFooter className="pt-0 flex gap-2">
            {teamPermissions.canView(backendUser?.permissions ?? []) && (
              <Button asChild variant="outline" size="sm" className="flex-1">
                <Link href={`/enterprise/teams/${team.id}/view`}>
                  <Info className="mr-2 h-4 w-4" />
                  {t('details')}
                </Link>
              </Button>
            )}
            
            {teamPermissions.canAssignUser(backendUser?.permissions ?? []) && (
              <Button asChild variant="outline" size="sm" className="flex-1">
                <Link href={`/enterprise/teams/${team.id}/members`}>
                  <UserPlus className="mr-2 h-4 w-4" />
                  {t('members')}
                </Link>
              </Button>
            )}
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

