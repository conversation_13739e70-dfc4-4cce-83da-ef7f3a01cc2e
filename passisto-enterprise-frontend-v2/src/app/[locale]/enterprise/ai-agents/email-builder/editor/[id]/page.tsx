"use client"

import { useEffect, useState, useRef } from "react"
import { EmailBuilder, type EmailComponent } from "../../_components/email-builder"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { EmailPreviewDialog } from "../../_components/email-preview-dialog"
import axiosInstance from "@/config/axios"
import { EMAIL_BUILDER_GET_BY_ID } from "@/utils/routes"
import { useAuth } from "@clerk/nextjs";
import { AxiosResponse } from "axios"
import { checkPermissions, emailBuilderPermissions } from "@/utils/ACTION_PERMISSIONS"
import { useBackendUser } from "@/hooks/useBackendUser";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface GeneratEmailResponse {
  id: String;
  companyId: String;
  title: string;
  description: string;
  fields: [];
}
export default function EditorPage({ params }: { params: { id: string } }) {
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const router = useRouter();

  const [initialComponents, setInitialComponents] = useState<EmailComponent[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [emailHTML, setEmailHTML] = useState<string>("")
  const emailBuilderRef = useRef<{ generateHTML: () => string } | null>(null)
  const getEmailById = async (emailId:String) => {
    try {
      console.log(emailId);
      const token = await getToken();

      const response: AxiosResponse<GeneratEmailResponse> = await axiosInstance.get(EMAIL_BUILDER_GET_BY_ID(emailId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      console.log(response.data.fields);
      setInitialComponents(response.data.fields)
      // return response.data.email;
    } catch (error: any) {
      console.error("Error fetching email:", error?.response?.data || error?.message);
      throw error;
    }
  };

  useEffect(() => {
  // Only check permissions after backendUser has loaded
  if (!backendUserLoading && backendUser) {
    const permissions = backendUser?.permissions ?? [];

    // Check if user has at least one email builder-related permission
    const hasAnyEmailBuilderPermission = checkPermissions(permissions, [
      emailBuilderPermissions.canViewTemplate,
      emailBuilderPermissions.canEditBuilder,
      emailBuilderPermissions.canDeleteBuilder,
      emailBuilderPermissions.canSendBuilderEmail,
    ]);

    // Redirect if user doesn't have any email builder-related permissions
    if (!hasAnyEmailBuilderPermission) {
      toast.error("Permission denied", {
        description:
          "You don't have permission to access the email builder page.",
      });
      router.back();
    }
  }
}, [backendUser, backendUserLoading, router]);

  
  useEffect(() => {
 
    getEmailById(params.id)
    setIsLoading(false)
  }, [params])

  // Function to get the HTML from the EmailBuilder component
  const getEmailHTML = (): string => {
    // Try to get fresh HTML from the ref if available
    if (emailBuilderRef.current) {
      return emailBuilderRef.current.generateHTML()
    }
    // Fall back to the stored HTML
    return emailHTML
  }

  // Function to update the HTML when it changes
  const handleHTMLUpdate = (html: string) => {
    setEmailHTML(html)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading editor...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="p-2 border-b bg-card flex items-center justify-between">
        <Link href="/enterprise/ai-agents/email-builder">
          <Button variant="outline" size="sm" className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            Back to Generator
          </Button>
        </Link>
        <EmailPreviewDialog generateHTML={getEmailHTML} />
      </div>
      <EmailBuilder initialComponents={initialComponents} onHTMLChange={handleHTMLUpdate} ref={emailBuilderRef} />
    </div>
  )
}

 