"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
// import Layout from "./components/layout"
import SearchBar from "./_components/search-bar"
import SearchResults from "./_components/search-results"
import SearchSettings from "./_components/search-settings"
import SelectedKnowledgeBases from "./_components/selected-knowledge-bases"
import { useAuth } from "@clerk/nextjs"
import { searchApi, type SearchSource } from "@/services/searchApi"
import { toast } from "@/hooks/use-toast"
import { useChatSettings } from "@/context/ChatSettingsContext"


export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [hasSearched, setHasSearched] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchSource[]>([])
  const [expandedQueries, setExpandedQueries] = useState<string[]>([])
  const { userId } = useAuth()
  const { getSelectedAliasIds } = useChatSettings()

  const handleSearch = async () => {
    if (!searchQuery) return
    setIsLoading(true)
    setShowSuggestions(false)

    try {
      // Get the selected alias IDs
      const selectedAliasIds = getSelectedAliasIds()

      // Use the search API to get results with the selected aliases
      // const response = await searchApi.search(searchQuery, userId ?? null, selectedAliasIds)
      const response = await searchApi.search(searchQuery, userId ?? null, selectedAliasIds)
      // Update state with the search results
      setSearchResults(response.sources)
      setExpandedQueries(response.expanded_queries)
      setHasSearched(true)
    } catch (err) {
      console.error("Search error:", err)
      toast({
        title: "Search Error",
        description: "Failed to retrieve search results. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    // <Layout>
      <div className={cn("w-full max-w-2xl mx-auto transition-all duration-300", hasSearched ? "mt-8" : "mt-32")}>
        <h1
          className={cn(
            "text-4xl md:text-6xl font-bold text-center bg-gradient-to-r from-primary to-primary-foreground bg-clip-text text-transparent pb-2 transition-all duration-300",
            hasSearched && "text-2xl md:text-3xl",
          )}
        >
          Passisto Search<span className="text-primary">Hub</span>
        </h1>

        <div className="mt-6 space-y-4">
          <div className="flex justify-center items-center gap-2">
            <SearchSettings />
          </div>

          <div className="flex justify-center">
            <SelectedKnowledgeBases className="mb-2" />
          </div>

          <SearchBar
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            showSuggestions={showSuggestions}
            setShowSuggestions={setShowSuggestions}
            handleSearch={handleSearch}
            setHasSearched={setHasSearched}
            isLoading={isLoading}
          />
        </div>

        {!hasSearched && (
          <div className="flex justify-center space-x-4 mt-6">
            <Button
              className="h-11 px-6 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105"
              onClick={handleSearch}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="mr-2">Searching</span>
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                </>
              ) : (
                "Search Now"
              )}
            </Button>
            {/* <Button
              variant="outline"
              className="h-11 px-6 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105"
            >
              I&apos;m Feeling Lucky
            </Button> */}
          </div>
        )}

        {hasSearched && <SearchResults isLoading={isLoading} searchResults={searchResults} expandedQueries={expandedQueries} />}
      </div>
    // </Layout>
  )
}

