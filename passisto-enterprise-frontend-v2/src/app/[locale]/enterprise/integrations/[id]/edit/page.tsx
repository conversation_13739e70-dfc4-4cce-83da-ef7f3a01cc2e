"use client";

import { useEffect, useState } from "react";
import { EditIntegrationForm } from "@/components/integrations/edit-integration-form";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import axiosInstance from "@/config/axios";
import { useParams } from "next/navigation";
import { ALL_INTEGRATIONS } from "@/utils/routes";
import { useAuth } from "@clerk/nextjs";
import {
  checkPermissions,
  integrationPermissions,
} from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

type Integration = {
  id: string;
  name: string;
  providerType: "jira" | "ftp" | "web";
  status: string;
  updateTime: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  jira?: {
    domain: string;
    project: string;
    email: string;
  };
  ftp?: {
    server: string;
    port: number;
    username: string;
    isSecure: boolean;
  };
  web?: {
    url: string;
  };
};

export default function EditIntegrationPage() {
  const params = useParams();
  const [integration, setIntegration] = useState<Integration | null>(null);
  const [isLoading, setLoading] = useState(true);
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const router = useRouter();

  useEffect(() => {
  if (!backendUserLoading && backendUser && integration) {
    const permissions = backendUser.permissions ?? [];

    let hasPermission = false;

    switch (integration.providerType) {
      case "ftp":
        hasPermission = integrationPermissions.canUpdateFTP(permissions);
        break;
      case "jira":
        hasPermission = integrationPermissions.canUpdateJira(permissions);
        break;
      case "web":
        hasPermission = integrationPermissions.canUpdateWeb(permissions);
        break;
      default:
        hasPermission = false;
    }

    if (!hasPermission) {
      toast.error("Permission denied", {
        description:
          "You don't have permission to edit this integration.",
      });
      router.back();
    }
  }
}, [backendUser, backendUserLoading, integration, router]);


  useEffect(() => {
    const fetchIntegration = async () => {
      try {
        const token = await getToken();
        const headers = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
        const res = await axiosInstance.get(
          `${ALL_INTEGRATIONS}/integration/${params.id}`,
          headers
        );
        if (res.status !== 200) throw new Error("Failed to fetch integration");
        setIntegration(res.data);
      } catch (error) {
        console.error("Error fetching integration:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchIntegration();
  }, [params.id]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 container py-6">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="ml-4 text-muted-foreground">
              Loading integration ...
            </p>
          </div>
        </main>
      </div>
    );
  }
  if (!integration) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <p className="text-lg font-medium text-red-500">
          Integration not found.
        </p>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <main className="flex-1 container py-10 px-4 md:px-8">
        {/* Header Section */}
        <div className="mb-8">
          <Link href={`/enterprise/integrations/${params.id}`}>
            <Button
              variant="ghost"
              size="sm"
              className="mb-5 flex items-center text-blue-600 hover:text-blue-700 transition-colors dark:text-blue-400 dark:hover:text-blue-500"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Integration Details
            </Button>
          </Link>
          <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 dark:text-white">
            Edit Integration
          </h1>
          <p className="mt-3 text-lg text-gray-600 dark:text-gray-400">
            Update the configuration for{" "}
            <span className="font-semibold">{integration.name}</span>.
          </p>
        </div>

        {/* Form Section */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg px-8 py-10">
          <div className="flex items-center mb-6">
            <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-full">
              <svg
                className="h-6 w-6 text-blue-600 dark:text-blue-300"
                fill="none"
                stroke="currentColor"
                strokeWidth={2}
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 4v16m8-8H4"
                />
              </svg>
            </div>
            <h2 className="ml-3 text-2xl font-semibold text-gray-800 dark:text-gray-100">
              Integration Details
            </h2>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
            Modify the fields below to update your integration settings.
          </p>
          <EditIntegrationForm integration={integration} />
        </div>
      </main>
    </div>
  );
}
