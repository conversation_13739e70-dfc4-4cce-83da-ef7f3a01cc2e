import { useState, useEffect, useCallback } from 'react';
import { chatbot<PERSON><PERSON>, ChatMessage, ChatSession } from '@/services/chatbotApi';
import { useBackendUser } from './useBackendUser';
import { useChatSettings } from '@/context/ChatSettingsContext';

export const useChatSession = (initialSessionId?: string) => {
  // const [sessionId, setSessionId] = useState<string>(initialSessionId ?? '');
  const [sessionId, setSessionId] = useState<string>(initialSessionId ?? chatbotApi.createNewSession());
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [sendingMessage, setSendingMessage] = useState<boolean>(false);
  const [switchingSession, setSwitchingSession] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { backendUser } = useBackendUser();
  const { getSelectedAliasIds } = useChatSettings();

  // Initialize with a welcome message when component mounts
  useEffect(() => {
    setMessages([{ role: 'assistant', content: 'Hello! How can I help you today?' }]);
  }, []);

  // Fetch chat history when session ID changes
  useEffect(() => {
    if (sessionId) {
      fetchChatHistory();
    }
  }, [sessionId]);

  // Fetch all chat sessions for the current user
  const fetchUserSessions = useCallback(async () => {
    if (!backendUser?.userId) {
      console.log('Waiting for backendUser to be available before fetching user sessions');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const userSessions = await chatbotApi.getUserSessions(backendUser.userId);
      setSessions(userSessions);
    } catch (err: any) {
      console.error('Error fetching user sessions:', err);
      let errorMsg = 'Failed to load chat sessions. Please try again.';

      if (err.code === 'ECONNREFUSED' || err.message?.includes('Network Error')) {
        errorMsg = 'Cannot connect to the chatbot server. Please check if the server is running on port 5921.';
      } else if (err.message?.includes('Cross-Origin Request Blocked') || err.message?.includes('CORS')) {
        errorMsg = 'CORS error: The chatbot server is not configured to accept requests from this origin.';
      } else if (err.response?.status === 404) {
        errorMsg = 'No chat sessions found for this user.';
      } else if (err.response?.data?.message) {
        errorMsg = `Server error: ${err.response.data.message}`;
      }

      setError(errorMsg);
      setSessions([]);
    } finally {
      setLoading(false);
    }
  }, [backendUser]);

  // Fetch chat history for the current session
  const fetchChatHistory = useCallback(async () => {
    if (!sessionId) return;
    if (!backendUser?.userId) {
      console.log('Waiting for backendUser to be available before fetching chat history');
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const history = await chatbotApi.getChatHistory(sessionId, backendUser.userId);
      if (history && history.length > 0) {
        setMessages(history);
      } else {
        setMessages([{ role: 'assistant', content: 'Hello! How can I help you today?' }]);
      }
    } catch (err: any) {
      console.error('Error fetching chat history:', err);
      if (err.response?.status === 404) {
        console.log('New session detected, showing welcome message');
        setMessages([{ role: 'assistant', content: 'Hello! How can I help you today?' }]);
        setError(null);
      } else {
        let errorMsg = 'Failed to load chat history. Please try again.';
        if (err.code === 'ECONNREFUSED' || err.message?.includes('Network Error')) {
          errorMsg = 'Cannot connect to the chatbot server. Please check if the server is running on port 5921.';
        } else if (err.message?.includes('Cross-Origin Request Blocked') || err.message?.includes('CORS')) {
          errorMsg = 'CORS error: The chatbot server is not configured to accept requests from this origin.';
        } else if (err.response?.data?.message) {
          errorMsg = `Server error: ${err.response.data.message}`;
        }
        setError(errorMsg);
        setMessages([{ role: 'assistant', content: 'Hello! How can I help you today?' }]);
      }
    } finally {
      setLoading(false);
    }
  }, [sessionId, backendUser]);

  // Send a message to the chatbot
  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;
    if (!backendUser?.userId) {
      const errorMsg = 'User information not available. Please try again in a moment.';
      setError(errorMsg);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `Sorry, I encountered an error: ${errorMsg}`
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }
    setSendingMessage(true);
    setError(null);
    // We should already have a session ID from initialization,
    // but create one if it somehow doesn't exist
    const currentSessionId = sessionId ?? chatbotApi.createNewSession();
    // If we had to create a new session ID, update the state
    if (currentSessionId !== sessionId) {
      setSessionId(currentSessionId);
    }
    const userMessage: ChatMessage = { role: 'user', content: message };
    setMessages(prev => [...prev, userMessage]);
    try {
      const selectedAliasIds = getSelectedAliasIds();
      // const aliasParam = selectedAliasIds.join(',');
      const aliasParam = selectedAliasIds;
      const response = await chatbotApi.sendMessage(
        message,
        currentSessionId,
        backendUser.userId,
        aliasParam
      );
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.answer,
        sources: response.sources && response.sources.length > 0 ? response.sources : undefined
      };
      setMessages(prev => [...prev, assistantMessage]);
      fetchUserSessions();
    } catch (err: any) {
      console.error('Error sending message:', err);
      let errorMsg = 'Failed to send message. Please try again.';
      if (err.code === 'ECONNREFUSED' || err.message?.includes('Network Error')) {
        errorMsg = 'Cannot connect to the chatbot server. Please check if the server is running on port 5921.';
      } else if (err.message?.includes('Cross-Origin Request Blocked') || err.message?.includes('CORS')) {
        errorMsg = 'CORS error: The chatbot server is not configured to accept requests from this origin.';
      } else if (err.response?.status === 404) {
        errorMsg = 'Chatbot API endpoint not found. Please check the API URL configuration.';
      } else if (err.response?.status === 401 || err.response?.status === 403) {
        errorMsg = 'Authentication error. You may not have permission to access the chatbot.';
      } else if (err.response?.data?.message) {
        errorMsg = `Server error: ${err.response.data.message}`;
      }
      setError(errorMsg);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `Sorry, I encountered an error: ${errorMsg}`
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setSendingMessage(false);
    }
  }, [sessionId, backendUser, fetchUserSessions, getSelectedAliasIds]);

  // Create a new chat session
  const createNewChat = useCallback(() => {
    try {
      // Set loading state
      setSwitchingSession(true);

      // Generate a new UUID for the session
      const newSessionId = chatbotApi.createNewSession();

      // Update state with the new session ID
      setSessionId(newSessionId);

      // Reset messages to show welcome message
      setMessages([{ role: 'assistant', content: 'Hello! How can I help you today?' }]);
      setError(null);

      fetchUserSessions();

      console.log(`Created new chat session with ID: ${newSessionId}`);
      return newSessionId;
    } catch (err) {
      console.error('Error creating new chat session:', err);
      setError('Failed to create a new chat session. Please try again.');
      setSwitchingSession(false);
    }
  }, [fetchUserSessions]);

  // Switch to an existing chat session
  const switchSession = useCallback((id: string) => {
    setSwitchingSession(true);
    setSessionId(id);
    setError(null);
    setMessages([{ role: 'assistant', content: 'Hello! How can I help you today?' }]);
    setTimeout(() => {
      setSwitchingSession(false);
    }, 300);
  }, []);

  // Delete a chat session
  const deleteSession = useCallback(async (id: string) => {
    if (!backendUser?.userId) {
      setError('User information not available. Please try again in a moment.');
      return { success: false };
    }

    setLoading(true);
    setError(null);

    try {
      await chatbotApi.deleteSession(id, backendUser.userId);
      if (id === sessionId) {
        const newId = chatbotApi.createNewSession();
        setSessionId(newId);
        setMessages([{ role: 'assistant', content: 'Hello! How can I help you today?' }]);
        await fetchUserSessions();
        return { success: true, newSessionId: newId };
      }
      await fetchUserSessions();
      return { success: true };
    } catch (err: any) {
      console.error('Error deleting session:', err);
      let errorMsg = 'Failed to delete chat session. Please try again.';
      if (err.code === 'ECONNREFUSED' || err.message?.includes('Network Error')) {
        errorMsg = 'Cannot connect to the chatbot server. Please check if the server is running on port 5921.';
      } else if (err.response?.status === 404) {
        await fetchUserSessions();
        return { success: true };
      } else if (err.response?.data?.message) {
        errorMsg = `Server error: ${err.response.data.message}`;
      }
      setError(errorMsg);
      return { success: false };
    } finally {
      setLoading(false);
    }
  }, [sessionId, backendUser, fetchUserSessions]);

  return {
    sessionId,
    messages,
    sessions,
    loading,
    sendingMessage,
    switchingSession,
    error,
    sendMessage,
    createNewChat,
    switchSession,
    deleteSession,
    fetchChatHistory,
    fetchUserSessions
  };
};
